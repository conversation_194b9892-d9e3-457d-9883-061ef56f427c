import { useEffect, useState } from 'react';

export type ConnectionDetails = {
  url: string;
  token: string;
  room?: string;
  identity?: string;
};

export function useConnectionDetails(): ConnectionDetails | null {
  const [connection, setConnection] = useState<ConnectionDetails | null>(null);
  const [userId, setUserId] = useState(132);

  useEffect(() => {
    if (!userId) return; // guard clause

    const fetchConnection = async () => {
      try {
        const url = `http://103.86.176.140:6324/api/get-connection-details?userId=${encodeURIComponent(userId)}`;
        const res = await fetch(url);
        if (!res.ok) {
          throw new Error('Failed to fetch connection details');
        }

        const data = await res.json();
        setConnection({
          url: data.url,
          token: data.token,
          room: data.room,
          identity: data.identity,
        });
      } catch (err) {
        console.error('Error fetching connection details:', err);
      }
    };

    fetchConnection();
  }, [userId]);

  return connection;
}
